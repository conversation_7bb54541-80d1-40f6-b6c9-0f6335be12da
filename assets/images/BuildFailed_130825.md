Running 'gradlew :app:assembleRelease' in /home/<USER>/workingdir/build/android
Welcome to Gradle 8.13!
Here are the highlights of this release:
- Daemon JVM auto-provisioning
 - Enhancements for Scala plugin and JUnit testing
 - Improvements for build authors and plugin developers
For more details see https://docs.gradle.org/8.13/release-notes.html
To honour the JVM settings for this build a single-use Daemon process will be forked. For more on this, please refer to https://docs.gradle.org/8.13/userguide/gradle_daemon.html#sec:disabling_the_daemon in the Gradle documentation.
Daemon will be stopped at the end of the build
> Configure project :expo-gradle-plugin:expo-autolinking-plugin-shared
w: file:///home/<USER>/workingdir/build/node_modules/expo-modules-autolinking/android/expo-gradle-plugin/expo-autolinking-plugin-shared/build.gradle.kts:32:9: The expression is unused
> Task :gradle-plugin:shared:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :gradle-plugin:settings-plugin:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:checkKotlinGradlePluginConfigurationErrors
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:checkKotlinGradlePluginConfigurationErrors
> Task :gradle-plugin:settings-plugin:pluginDescriptors
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:pluginDescriptors
> Task :gradle-plugin:settings-plugin:processResources
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:processResources
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:processResources NO-SOURCE
> Task :gradle-plugin:shared:processResources NO-SOURCE
> Task :gradle-plugin:shared:compileKotlin
> Task :gradle-plugin:shared:compileJava NO-SOURCE
> Task :gradle-plugin:shared:classes UP-TO-DATE
> Task :gradle-plugin:shared:jar
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:compileKotlin
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:compileJava NO-SOURCE
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:classes UP-TO-DATE
> Task :expo-gradle-plugin:expo-autolinking-plugin-shared:jar
> Task :gradle-plugin:settings-plugin:compileKotlin
> Task :gradle-plugin:settings-plugin:compileJava NO-SOURCE
> Task :gradle-plugin:settings-plugin:classes
> Task :gradle-plugin:settings-plugin:jar
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:compileKotlin
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:compileJava NO-SOURCE
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:classes
> Task :expo-gradle-plugin:expo-autolinking-settings-plugin:jar
> Task :expo-gradle-plugin:expo-autolinking-plugin:checkKotlinGradlePluginConfigurationErrors
> Task :expo-module-gradle-plugin:checkKotlinGradlePluginConfigurationErrors
> Task :gradle-plugin:react-native-gradle-plugin:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-dev-launcher-gradle-plugin:checkKotlinGradlePluginConfigurationErrors
> Task :expo-module-gradle-plugin:pluginDescriptors
> Task :expo-dev-launcher-gradle-plugin:pluginDescriptors
> Task :expo-module-gradle-plugin:processResources
> Task :expo-dev-launcher-gradle-plugin:processResources
> Task :expo-gradle-plugin:expo-autolinking-plugin:pluginDescriptors
> Task :expo-gradle-plugin:expo-autolinking-plugin:processResources
> Task :gradle-plugin:react-native-gradle-plugin:pluginDescriptors
> Task :gradle-plugin:react-native-gradle-plugin:processResources
> Task :expo-gradle-plugin:expo-autolinking-plugin:compileKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-modules-autolinking/android/expo-gradle-plugin/expo-autolinking-plugin/src/main/kotlin/expo/modules/plugin/ExpoAutolinkingPlugin.kt:29:71 Name shadowed: project
> Task :expo-gradle-plugin:expo-autolinking-plugin:compileJava
NO-SOURCE
> Task :expo-gradle-plugin:expo-autolinking-plugin:classes
> Task :expo-gradle-plugin:expo-autolinking-plugin:jar
> Task :gradle-plugin:react-native-gradle-plugin:compileKotlin
> Task :gradle-plugin:react-native-gradle-plugin:compileJava NO-SOURCE
> Task :gradle-plugin:react-native-gradle-plugin:classes
> Task :gradle-plugin:react-native-gradle-plugin:jar
> Task :expo-dev-launcher-gradle-plugin:compileKotlin
> Task :expo-dev-launcher-gradle-plugin:compileJava NO-SOURCE
> Task :expo-dev-launcher-gradle-plugin:classes
> Task :expo-dev-launcher-gradle-plugin:jar
> Task :expo-module-gradle-plugin:compileKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-modules-core/expo-module-gradle-plugin/src/main/kotlin/expo/modules/plugin/android/AndroidLibraryExtension.kt:9:24 'targetSdk: Int?' is deprecated. Will be removed from library DSL in v9.0. Use testOptions.targetSdk or/and lint.targetSdk instead
> Task :expo-module-gradle-plugin:compileJava NO-SOURCE
> Task :expo-module-gradle-plugin:classes
> Task :expo-module-gradle-plugin:jar
> Configure project :
[32m[ExpoRootProject][0m Using the following versions:
  - buildTools:  [32m35.0.0[0m
  - minSdk:      [32m24[0m
  - compileSdk:  [32m35[0m
  - targetSdk:   [32m35[0m
  - ndk:         [32m27.1.12297006[0m
  - kotlin:      [32m2.0.21[0m
  - ksp:         [32m2.0.21-1.0.28[0m
> Configure project :app
 ℹ️  [33mApplying gradle plugin[0m '[32mexpo-dev-launcher-gradle-plugin[0m'
Checking the license for package NDK (Side by side) 27.1.12297006 in /home/<USER>/Android/Sdk/licenses
License for package NDK (Side by side) 27.1.12297006 accepted.
Preparing "Install NDK (Side by side) 27.1.12297006 v.27.1.12297006".
"Install NDK (Side by side) 27.1.12297006 v.27.1.12297006" ready.
Installing NDK (Side by side) 27.1.12297006 in /home/<USER>/Android/Sdk/ndk/27.1.12297006
"Install NDK (Side by side) 27.1.12297006 v.27.1.12297006" complete.
"Install NDK (Side by side) 27.1.12297006 v.27.1.12297006" finished.
> Configure project :expo
Using expo modules
  - [32mexpo-constants[0m (17.1.7)
  - [32mexpo-dev-client[0m (5.2.4)
  - [32mexpo-dev-launcher[0m (5.1.16)
  - [32mexpo-dev-menu[0m (6.1.14)
  - [32mexpo-dev-menu-interface[0m (1.10.0)
  - [32mexpo-image-loader[0m (5.1.0)
  - [32mexpo-image-manipulator[0m (13.1.7)
  - [32mexpo-json-utils[0m (0.15.0)
  - [32mexpo-manifests[0m (0.16.6)
- [32mexpo-modules-core[0m (2.5.0)
- [32mexpo-updates-interface[0m (1.1.0)
- [33m[📦][0m [32mexpo-application[0m (6.1.5)
  - [33m[📦][0m [32mexpo-asset[0m (11.1.7)
  - [33m[📦][0m [32mexpo-blur[0m (14.1.5)
  - [33m[📦][0m [32mexpo-camera[0m (16.1.11)
  - [33m[📦][0m [32mexpo-crypto[0m (14.1.5)
  - [33m[📦][0m [32mexpo-file-system[0m (18.1.11)
  - [33m[📦][0m [32mexpo-font[0m (13.3.2)
  - [33m[📦][0m [32mexpo-haptics[0m (14.1.4)
  - [33m[📦][0m [32mexpo-image[0m (2.4.0)
  - [33m[📦][0m [32mexpo-image-picker[0m (16.1.4)
  - [33m[📦][0m [32mexpo-keep-awake[0m (14.1.4)
  - [33m[📦][0m [32mexpo-linear-gradient[0m (14.1.5)
  - [33m[📦][0m [32mexpo-linking[0m (7.1.7)
  - [33m[📦][0m [32mexpo-location[0m (18.1.6)
  - [33m[📦][0m [32mexpo-splash-screen[0m (0.30.10)
  - [33m[📦][0m [32mexpo-system-ui[0m (5.0.10)
  - [33m[📦][0m [32mexpo-web-browser[0m (14.2.0)
> Configure project :react-native-reanimated
Android gradle plugin: 8.8.2
Gradle: 8.13
> Task :expo-dev-client:preBuild UP-TO-DATE
> Task :expo-dev-client:preReleaseBuild UP-TO-DATE
> Task :expo-dev-client:writeReleaseAarMetadata
> Task :expo-dev-launcher:preBuild UP-TO-DATE
> Task :expo-dev-launcher:preReleaseBuild UP-TO-DATE
> Task :expo-dev-launcher:writeReleaseAarMetadata
> Task :expo-dev-menu:clenupAssets UP-TO-DATE
> Task :app:buildKotlinToolingMetadata
> Task :app:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-dev-menu:copyAssets
> Task :expo-dev-menu:preBuild
> Task :expo-dev-menu:preReleaseBuild
> Task :expo-dev-menu:writeReleaseAarMetadata
> Task :expo-dev-menu-interface:preBuild UP-TO-DATE
> Task :expo-dev-menu-interface:preReleaseBuild UP-TO-DATE
> Task :expo-dev-menu-interface:writeReleaseAarMetadata
> Task :expo-image-loader:preBuild UP-TO-DATE
> Task :expo-image-loader:preReleaseBuild UP-TO-DATE
> Task :app:generateAutolinkingNewArchitectureFiles
> Task :expo-image-loader:writeReleaseAarMetadata
> Task :expo-image-manipulator:preBuild UP-TO-DATE
> Task :expo-image-manipulator:preReleaseBuild UP-TO-DATE
> Task :expo-image-manipulator:writeReleaseAarMetadata
> Task :expo-json-utils:preBuild UP-TO-DATE
> Task :app:generateAutolinkingPackageList
> Task :expo-json-utils:preReleaseBuild UP-TO-DATE
> Task :app:generateCodegenSchemaFromJavaScript SKIPPED
> Task :app:generateCodegenArtifactsFromSchema SKIPPED
> Task :app:preBuild
> Task :app:preReleaseBuild
> Task :expo-json-utils:writeReleaseAarMetadata
> Task :expo-manifests:preBuild UP-TO-DATE
> Task :expo-manifests:preReleaseBuild UP-TO-DATE
> Task :expo-manifests:writeReleaseAarMetadata
> Task :expo-modules-core:preBuild UP-TO-DATE
> Task :expo-modules-core:preReleaseBuild UP-TO-DATE
> Task :app:generateReleaseBuildConfig
> Task :expo-modules-core:writeReleaseAarMetadata
> Task :expo-updates-interface:preBuild UP-TO-DATE
> Task :expo-updates-interface:preReleaseBuild UP-TO-DATE
> Task :expo-updates-interface:writeReleaseAarMetadata
> Task :expo:generatePackagesList
> Task :expo:preBuild
> Task :expo:preReleaseBuild
> Task :expo:writeReleaseAarMetadata
> Task :react-native-reanimated:assertMinimalReactNativeVersionTask SKIPPED
> Task :expo-constants:createExpoConfig
The NODE_ENV environment variable is required but was not specified. Ensure the project is bundled with Expo CLI or NODE_ENV is set. Using only .env.local and .env
[dotenv@17.2.1] injecting env (0) from .env.local -- tip: ⚙️  write to custom object with { processEnv: myObject }
> Task :expo-constants:preBuild
> Task :expo-constants:preReleaseBuild
> Task :expo-constants:writeReleaseAarMetadata
> Task :react-native-async-storage_async-storage:generateCodegenSchemaFromJavaScript
> Task :react-native-gesture-handler:generateCodegenSchemaFromJavaScript
> Task :react-native-reanimated:generateCodegenSchemaFromJavaScript
> Task :react-native-safe-area-context:generateCodegenSchemaFromJavaScript
> Task :react-native-async-storage_async-storage:generateCodegenArtifactsFromSchema
> Task :react-native-async-storage_async-storage:preBuild
> Task :react-native-async-storage_async-storage:preReleaseBuild
> Task :react-native-async-storage_async-storage:writeReleaseAarMetadata
> Task :react-native-reanimated:generateCodegenArtifactsFromSchema
> Task :react-native-gesture-handler:generateCodegenArtifactsFromSchema
> Task :react-native-gesture-handler:preBuild
> Task :react-native-gesture-handler:preReleaseBuild
> Task :react-native-gesture-handler:writeReleaseAarMetadata
> Task :react-native-reanimated:prepareReanimatedHeadersForPrefabs
> Task :react-native-reanimated:prepareWorkletsHeadersForPrefabs
> Task :react-native-reanimated:preBuild
> Task :react-native-reanimated:preReleaseBuild
> Task :react-native-reanimated:writeReleaseAarMetadata
> Task :react-native-safe-area-context:generateCodegenArtifactsFromSchema
> Task :react-native-safe-area-context:preBuild
> Task :react-native-safe-area-context:preReleaseBuild
> Task :react-native-safe-area-context:writeReleaseAarMetadata
> Task :react-native-worklets:assertMinimalReactNativeVersionTask SKIPPED
> Task :react-native-worklets:assertNewArchitectureEnabledTask SKIPPED
> Task :react-native-screens:generateCodegenSchemaFromJavaScript
> Task :react-native-webview:generateCodegenSchemaFromJavaScript
> Task :react-native-svg:generateCodegenSchemaFromJavaScript
> Task :react-native-worklets:generateCodegenSchemaFromJavaScript
> Task :react-native-screens:generateCodegenArtifactsFromSchema
> Task :react-native-screens:preBuild
> Task :react-native-screens:preReleaseBuild
> Task :react-native-screens:writeReleaseAarMetadata
> Task :react-native-webview:generateCodegenArtifactsFromSchema
> Task :react-native-webview:preBuild
> Task :react-native-webview:preReleaseBuild
> Task :react-native-webview:writeReleaseAarMetadata
> Task :expo:generateReleaseResValues
> Task :react-native-svg:generateCodegenArtifactsFromSchema
> Task :react-native-svg:preBuild
> Task :react-native-svg:preReleaseBuild
> Task :expo:generateReleaseResources
> Task :react-native-svg:writeReleaseAarMetadata
> Task :react-native-worklets:generateCodegenArtifactsFromSchema
> Task :expo-constants:generateReleaseResValues
> Task :expo-constants:generateReleaseResources
> Task :react-native-worklets:prepareWorkletsHeadersForPrefabs
> Task :react-native-worklets:preBuild
> Task :react-native-worklets:preReleaseBuild
> Task :react-native-worklets:writeReleaseAarMetadata
> Task :expo-dev-client:generateReleaseResValues
> Task :expo-dev-client:generateReleaseResources
> Task :expo-constants:packageReleaseResources
> Task :expo:packageReleaseResources
> Task :expo-dev-launcher:generateReleaseResValues
> Task :expo-dev-menu:generateReleaseResValues
> Task :expo-dev-client:packageReleaseResources
> Task :expo-dev-menu-interface:generateReleaseResValues
> Task :expo-dev-menu-interface:generateReleaseResources
> Task :expo-dev-menu-interface:packageReleaseResources
> Task :expo-image-loader:generateReleaseResValues
> Task :expo-image-loader:generateReleaseResources
> Task :expo-dev-menu:generateReleaseResources
> Task :expo-dev-launcher:generateReleaseResources
> Task :expo-image-loader:packageReleaseResources
> Task :expo-image-manipulator:generateReleaseResValues
> Task :expo-image-manipulator:generateReleaseResources
> Task :expo-image-manipulator:packageReleaseResources
> Task :expo-json-utils:generateReleaseResValues
> Task :expo-json-utils:generateReleaseResources
> Task :expo-json-utils:packageReleaseResources
> Task :expo-manifests:generateReleaseResValues
> Task :expo-manifests:generateReleaseResources
> Task :expo-manifests:packageReleaseResources
> Task :expo-modules-core:generateReleaseResValues
> Task :expo-modules-core:generateReleaseResources
> Task :expo-modules-core:packageReleaseResources
> Task :expo-updates-interface:generateReleaseResValues
> Task :expo-updates-interface:generateReleaseResources
> Task :expo-dev-menu:packageReleaseResources
> Task :expo-updates-interface:packageReleaseResources
> Task :react-native-gesture-handler:generateReleaseResValues
> Task :react-native-async-storage_async-storage:generateReleaseResValues
> Task :react-native-gesture-handler:generateReleaseResources
> Task :react-native-async-storage_async-storage:generateReleaseResources
> Task :react-native-gesture-handler:packageReleaseResources
> Task :react-native-async-storage_async-storage:packageReleaseResources
> Task :react-native-safe-area-context:generateReleaseResValues
> Task :react-native-reanimated:generateReleaseResValues
> Task :react-native-reanimated:generateReleaseResources
> Task :react-native-safe-area-context:generateReleaseResources
> Task :react-native-reanimated:packageReleaseResources
> Task :react-native-safe-area-context:packageReleaseResources
> Task :react-native-screens:generateReleaseResValues
> Task :react-native-svg:generateReleaseResValues
> Task :react-native-screens:generateReleaseResources
> Task :react-native-svg:generateReleaseResources
> Task :react-native-svg:packageReleaseResources
> Task :react-native-webview:generateReleaseResValues
> Task :react-native-webview:generateReleaseResources
> Task :react-native-webview:packageReleaseResources
> Task :react-native-screens:packageReleaseResources
> Task :react-native-worklets:generateReleaseResValues
> Task :expo:extractDeepLinksRelease
> Task :react-native-worklets:generateReleaseResources
> Task :react-native-worklets:packageReleaseResources
> Task :expo-constants:extractDeepLinksRelease
> Task :expo-dev-launcher:packageReleaseResources
> Task :expo-dev-client:extractDeepLinksRelease
> Task :expo-dev-client:processReleaseManifest
> Task :expo-constants:processReleaseManifest
> Task :expo:processReleaseManifest
> Task :expo-dev-menu:extractDeepLinksRelease
> Task :expo-dev-menu-interface:extractDeepLinksRelease
> Task :expo-dev-launcher:extractDeepLinksRelease
> Task :expo-dev-menu-interface:processReleaseManifest
> Task :expo-dev-menu:processReleaseManifest
> Task :expo-dev-launcher:processReleaseManifest
> Task :expo-image-loader:extractDeepLinksRelease
> Task :expo-json-utils:extractDeepLinksRelease
> Task :expo-image-manipulator:extractDeepLinksRelease
> Task :expo-image-manipulator:processReleaseManifest
> Task :expo-json-utils:processReleaseManifest
> Task :expo-image-loader:processReleaseManifest
> Task :expo-manifests:extractDeepLinksRelease
> Task :expo-updates-interface:extractDeepLinksRelease
> Task :expo-modules-core:extractDeepLinksRelease
> Task :expo-manifests:processReleaseManifest
> Task :expo-updates-interface:processReleaseManifest
> Task :react-native-async-storage_async-storage:extractDeepLinksRelease
> Task :expo-modules-core:processReleaseManifest
/home/<USER>/workingdir/build/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:8:9-11:45 Warning:
	meta-data#com.facebook.soloader.enabled@android:value was tagged at AndroidManifest.xml:8 to replace other declarations but no other declaration present
> Task :react-native-gesture-handler:extractDeepLinksRelease
> Task :react-native-reanimated:extractDeepLinksRelease
> Task :react-native-async-storage_async-storage:processReleaseManifest
package="com.reactnativecommunity.asyncstorage" found in source AndroidManifest.xml: /home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.reactnativecommunity.asyncstorage" from the source AndroidManifest.xml: /home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/src/main/AndroidManifest.xml.
> Task :react-native-safe-area-context:extractDeepLinksRelease
> Task :react-native-gesture-handler:processReleaseManifest
> Task :react-native-screens:extractDeepLinksRelease
> Task :react-native-reanimated:processReleaseManifest
> Task :react-native-safe-area-context:processReleaseManifest
package="com.th3rdwave.safeareacontext" found in source AndroidManifest.xml: /home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.th3rdwave.safeareacontext" from the source AndroidManifest.xml: /home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml.
> Task :react-native-svg:extractDeepLinksRelease
> Task :react-native-screens:processReleaseManifest
> Task :react-native-webview:extractDeepLinksRelease
> Task :react-native-worklets:extractDeepLinksRelease
> Task :react-native-svg:processReleaseManifest
> Task :react-native-worklets:processReleaseManifest
> Task :react-native-webview:processReleaseManifest
> Task :expo-dev-client:compileReleaseLibraryResources
> Task :expo:compileReleaseLibraryResources
> Task :expo-constants:compileReleaseLibraryResources
> Task :expo-constants:parseReleaseLocalResources
> Task :expo-dev-client:parseReleaseLocalResources
> Task :expo:parseReleaseLocalResources
> Task :expo:generateReleaseRFile
> Task :expo-dev-client:generateReleaseRFile
> Task :expo-constants:generateReleaseRFile
> Task :expo-dev-menu-interface:compileReleaseLibraryResources
> Task :app:createBundleReleaseJsAndAssets
[dotenv@17.2.1] injecting env (0) from .env.production -- tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild
[dotenv@17.2.1] injecting env (0) from .env.production -- tip: 📡 auto-backup env with Radar: https://dotenvx.com/radar
Starting Metro Bundler
[dotenv@17.2.1] injecting env (0) from .env.production -- tip: ⚙️  override existing env vars with { override: true }
> Task :expo-dev-launcher:parseReleaseLocalResources
> Task :expo-dev-launcher:compileReleaseLibraryResources
> Task :expo-dev-launcher:generateReleaseRFile
> Task :expo-dev-menu:parseReleaseLocalResources
> Task :expo-dev-menu:compileReleaseLibraryResources
> Task :expo-image-loader:compileReleaseLibraryResources
> Task :expo-dev-menu-interface:parseReleaseLocalResources
> Task :expo-dev-menu:generateReleaseRFile
> Task :expo-dev-menu-interface:generateReleaseRFile
> Task :expo-image-loader:parseReleaseLocalResources
> Task :expo-image-manipulator:compileReleaseLibraryResources
> Task :expo-json-utils:compileReleaseLibraryResources
> Task :expo-image-loader:generateReleaseRFile
> Task :expo-manifests:compileReleaseLibraryResources
> Task :expo-image-manipulator:parseReleaseLocalResources
> Task :expo-json-utils:parseReleaseLocalResources
> Task :expo-manifests:parseReleaseLocalResources
> Task :expo-image-manipulator:generateReleaseRFile
> Task :expo-json-utils:generateReleaseRFile
> Task :expo-modules-core:compileReleaseLibraryResources
> Task :expo-updates-interface:compileReleaseLibraryResources
> Task :expo-updates-interface:parseReleaseLocalResources
> Task :expo-manifests:generateReleaseRFile
> Task :expo-modules-core:parseReleaseLocalResources
> Task :react-native-async-storage_async-storage:compileReleaseLibraryResources
> Task :expo-modules-core:generateReleaseRFile
> Task :expo-updates-interface:generateReleaseRFile
> Task :react-native-reanimated:compileReleaseLibraryResources
> Task :react-native-async-storage_async-storage:parseReleaseLocalResources
> Task :react-native-gesture-handler:compileReleaseLibraryResources
> Task :react-native-reanimated:parseReleaseLocalResources
> Task :react-native-gesture-handler:parseReleaseLocalResources
> Task :react-native-async-storage_async-storage:generateReleaseRFile
> Task :react-native-gesture-handler:generateReleaseRFile
> Task :react-native-safe-area-context:compileReleaseLibraryResources
> Task :react-native-reanimated:generateReleaseRFile
> Task :react-native-safe-area-context:parseReleaseLocalResources
> Task :react-native-svg:compileReleaseLibraryResources
> Task :react-native-screens:parseReleaseLocalResources
> Task :react-native-safe-area-context:generateReleaseRFile
> Task :react-native-screens:compileReleaseLibraryResources
> Task :react-native-svg:parseReleaseLocalResources
> Task :react-native-screens:generateReleaseRFile
> Task :react-native-webview:compileReleaseLibraryResources
> Task :react-native-worklets:compileReleaseLibraryResources
> Task :react-native-webview:parseReleaseLocalResources
> Task :react-native-svg:generateReleaseRFile
> Task :expo:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-worklets:parseReleaseLocalResources
> Task :expo:generateReleaseBuildConfig
> Task :expo-constants:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-constants:generateReleaseBuildConfig
> Task :expo-modules-core:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-webview:generateReleaseRFile
> Task :react-native-worklets:generateReleaseRFile
> Task :expo-dev-client:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-modules-core:generateReleaseBuildConfig
> Task :expo-constants:javaPreCompileRelease
> Task :expo-dev-launcher:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-dev-client:dataBindingMergeDependencyArtifactsRelease
> Task :expo-dev-client:dataBindingGenBaseClassesRelease
> Task :expo-dev-client:generateReleaseBuildConfig
> Task :app:createBundleReleaseJsAndAssets
Android node_modules/expo-router/entry.js ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓░ 96.5% (2690/2739)
Android Bundled 3730ms node_modules/expo-router/entry.js (3133 modules)
Writing bundle output to: /home/<USER>/workingdir/build/android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.bundle
Writing sourcemap output to: /home/<USER>/workingdir/build/android/app/build/intermediates/sourcemaps/react/release/index.android.bundle.packager.map
Copying 26 asset files
> Task :expo-dev-client:javaPreCompileRelease
> Task :expo-dev-menu:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-dev-menu:generateReleaseBuildConfig
> Task :expo-dev-menu-interface:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-dev-menu-interface:generateReleaseBuildConfig
> Task :expo-dev-menu-interface:javaPreCompileRelease
> Task :expo-json-utils:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-json-utils:generateReleaseBuildConfig
> Task :expo-json-utils:javaPreCompileRelease
> Task :expo-manifests:checkKotlinGradlePluginConfigurationErrors
SKIPPED
> Task :expo-manifests:generateReleaseBuildConfig
> Task :expo-manifests:javaPreCompileRelease
> Task :expo-dev-menu:javaPreCompileRelease
> Task :expo-updates-interface:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-updates-interface:generateReleaseBuildConfig
> Task :expo-updates-interface:javaPreCompileRelease
> Task :expo-image-loader:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-image-loader:generateReleaseBuildConfig
> Task :expo-image-loader:javaPreCompileRelease
> Task :expo-image-manipulator:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :expo-image-manipulator:generateReleaseBuildConfig
> Task :expo-image-manipulator:javaPreCompileRelease
> Task :expo:javaPreCompileRelease
> Task :react-native-async-storage_async-storage:generateReleaseBuildConfig
> Task :react-native-async-storage_async-storage:javaPreCompileRelease
> Task :app:createBundleReleaseJsAndAssets
Done writing bundle output
Done writing sourcemap output
> Task :expo-dev-launcher:dataBindingMergeDependencyArtifactsRelease
> Task :expo-modules-core:javaPreCompileRelease
> Task :react-native-async-storage_async-storage:compileReleaseJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.
Note: /home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/src/javaPackage/java/com/reactnativecommunity/asyncstorage/AsyncStoragePackage.java uses unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
> Task :expo-dev-launcher:dataBindingGenBaseClassesRelease
> Task :expo-dev-launcher:generateReleaseBuildConfig
> Task :expo-dev-launcher:javaPreCompileRelease
> Task :react-native-async-storage_async-storage:bundleLibCompileToJarRelease
> Task :react-native-gesture-handler:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-reanimated:generateReleaseBuildConfig
> Task :react-native-gesture-handler:generateReleaseBuildConfig
> Task :react-native-reanimated:javaPreCompileRelease
> Task :react-native-reanimated:packageNdkLibs NO-SOURCE
> Task :react-native-gesture-handler:javaPreCompileRelease
> Task :react-native-safe-area-context:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-safe-area-context:generateReleaseBuildConfig
> Task :react-native-reanimated:compileReleaseJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.
Note: Some input files use unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
> Task :react-native-reanimated:bundleLibCompileToJarRelease
> Task :react-native-safe-area-context:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaView.kt:59:23 'val uiImplementation: UIImplementation!' is deprecated. Deprecated in Java.
> Task :react-native-safe-area-context:javaPreCompileRelease
> Task :react-native-safe-area-context:compileReleaseJavaWithJavac
> Task :react-native-safe-area-context:bundleLibCompileToJarRelease
> Task :react-native-screens:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-screens:generateReleaseBuildConfig
> Task :expo-modules-core:compileReleaseKotlin
> Task :app:checkReleaseAarMetadata
> Task :app:generateReleaseResValues
> Task :app:mapReleaseSourceSetPaths
> Task :app:generateReleaseResources
> Task :react-native-gesture-handler:compileReleaseKotlin
> Task :react-native-gesture-handler:compileReleaseJavaWithJavac
> Task :react-native-gesture-handler:bundleLibCompileToJarRelease
> Task :react-native-screens:javaPreCompileRelease
> Task :react-native-svg:generateReleaseBuildConfig
> Task :react-native-svg:javaPreCompileRelease
> Task :react-native-svg:compileReleaseJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.
Note: Some input files use unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
> Task :react-native-screens:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/RNScreensPackage.kt:66:17 'constructor(name: String, className: String, canOverrideExistingModule: Boolean, needsEagerInit: Boolean, hasConstants: Boolean, isCxxModule: Boolean, isTurboModule: Boolean): ReactModuleInfo' is deprecated. This constructor is deprecated and will be removed in the future. Use ReactModuleInfo(String, String, boolean, boolean, boolean, boolean)].
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/Screen.kt:46:77 Unchecked cast of '(androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior<android.view.View!>?..androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior<*>?)' to 'com.google.android.material.bottomsheet.BottomSheetBehavior<com.swmansion.rnscreens.Screen>'.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/Screen.kt:378:36 'fun setTranslucent(screen: Screen, activity: Activity?, context: ReactContext?): Unit' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/Screen.kt:397:36 'fun setColor(screen: Screen, activity: Activity?, context: ReactContext?): Unit' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/Screen.kt:415:36 'fun setNavigationBarColor(screen: Screen, activity: Activity?): Unit' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/Screen.kt:432:36 'fun setNavigationBarTranslucent(screen: Screen, activity: Activity?): Unit' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:217:31 'var targetElevation: Float' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:220:13 'fun setHasOptionsMenu(p0: Boolean): Unit' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:397:18 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:404:22 'fun onPrepareOptionsMenu(p0: Menu): Unit' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:407:18 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:412:22 'fun onCreateOptionsMenu(p0: Menu, p1: MenuInflater): Unit' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:7:8 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:210:9 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:212:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:214:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:7:8 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:204:14 'var statusBarColor: Int?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:221:14 'var isStatusBarTranslucent: Boolean?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:238:14 'var navigationBarColor: Int?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:247:14 'var isNavigationBarTranslucent: Boolean?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:382:48 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:383:49 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:384:45 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:385:52 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:386:48 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:387:51 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:388:56 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:389:57 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:390:51 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:55:42 'fun replaceSystemWindowInsets(p0: Int, p1: Int, p2: Int, p3: Int): WindowInsetsCompat' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:56:39 'val systemWindowInsetLeft: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:58:39 'val systemWindowInsetRight: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:59:39 'val systemWindowInsetBottom: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:102:53 'var statusBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:106:37 'var statusBarColor: Int?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:113:48 'var statusBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:116:32 'var statusBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:162:49 'var isStatusBarTranslucent: Boolean?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:218:43 'var navigationBarColor: Int?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:218:72 'var navigationBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:224:16 'var navigationBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:241:55 'var isNavigationBarTranslucent: Boolean?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:283:13 'fun setColor(screen: Screen, activity: Activity?, context: ReactContext?): Unit' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:285:13 'fun setTranslucent(screen: Screen, activity: Activity?, context: ReactContext?): Unit' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:289:13 'fun setNavigationBarColor(screen: Screen, activity: Activity?): Unit' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:290:13 'fun setNavigationBarTranslucent(screen: Screen, activity: Activity?): Unit' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:354:42 'var statusBarColor: Int?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:356:48 'var isStatusBarTranslucent: Boolean?' is deprecated. For apps targeting SDK 35 or above this prop has no effect because edge-to-edge is enabled by default and the status bar is always translucent.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:359:57 'var navigationBarColor: Int?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:360:63 'var isNavigationBarTranslucent: Boolean?' is deprecated. For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:5:8 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:142:9 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:144:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:146:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:148:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:150:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:152:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:154:13 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/bottomsheet/BottomSheetDialogRootView.kt:7:8 'object ReactFeatureFlags : Any' is deprecated. Use com.facebook.react.internal.featureflags.ReactNativeFeatureFlags instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/bottomsheet/BottomSheetDialogRootView.kt:25:13 'object ReactFeatureFlags : Any' is deprecated. Use com.facebook.react.internal.featureflags.ReactNativeFeatureFlags instead.
> Task :app:packageReleaseResources
> Task :app:parseReleaseLocalResources
> Task :app:createReleaseCompatibleScreenManifests
> Task :app:extractDeepLinksRelease
> Task :app:processReleaseMainManifest
/home/<USER>/workingdir/build/android/app/src/main/AndroidManifest.xml Warning:
	provider#expo.modules.filesystem.FileSystemFileProvider@android:authorities was tagged at AndroidManifest.xml:0 to replace other declarations but no other declaration present
> Task :app:processReleaseManifest
> Task :app:processReleaseManifestForPackage
> Task :react-native-svg:bundleLibCompileToJarRelease
> Task :react-native-webview:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :react-native-webview:generateReleaseBuildConfig
> Task :react-native-screens:compileReleaseJavaWithJavac
> Task :react-native-screens:bundleLibCompileToJarRelease
> Task :react-native-worklets:generateReleaseBuildConfig
> Task :react-native-webview:javaPreCompileRelease
> Task :react-native-worklets:javaPreCompileRelease
> Task :react-native-worklets:compileReleaseJavaWithJavac
Note: /home/<USER>/workingdir/build/node_modules/react-native-worklets/android/src/main/java/com/swmansion/worklets/WorkletsPackage.java uses unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
> Task :react-native-worklets:bundleLibCompileToJarRelease
> Task :app:javaPreCompileRelease
> Task :app:extractProguardFiles
> Task :expo:extractProguardFiles
> Task :expo-constants:extractProguardFiles
> Task :expo-modules-core:extractProguardFiles
> Task :expo-modules-core:prepareLintJarForPublish
> Task :expo-constants:prepareLintJarForPublish
> Task :expo-dev-client:extractProguardFiles
> Task :expo-dev-client:prepareLintJarForPublish
> Task :expo-dev-launcher:extractProguardFiles
> Task :expo-dev-menu:extractProguardFiles
> Task :expo-dev-menu-interface:extractProguardFiles
> Task :expo-dev-menu-interface:prepareLintJarForPublish
> Task :expo-json-utils:extractProguardFiles
> Task :expo-json-utils:prepareLintJarForPublish
> Task :expo-manifests:extractProguardFiles
> Task :expo-manifests:prepareLintJarForPublish
> Task :expo-dev-menu:prepareLintJarForPublish
> Task :expo-updates-interface:extractProguardFiles
> Task :expo-updates-interface:prepareLintJarForPublish
> Task :expo-dev-launcher:prepareLintJarForPublish
> Task :expo-image-loader:extractProguardFiles
> Task :expo-image-loader:prepareLintJarForPublish
> Task :expo-image-manipulator:extractProguardFiles
> Task :expo-image-manipulator:prepareLintJarForPublish
> Task :expo:prepareLintJarForPublish
> Task :react-native-async-storage_async-storage:bundleLibRuntimeToJarRelease
> Task :react-native-async-storage_async-storage:processReleaseJavaRes NO-SOURCE
> Task :react-native-async-storage_async-storage:createFullJarRelease
> Task :react-native-async-storage_async-storage:extractProguardFiles
> Task :app:mergeReleaseResources
> Task :expo-modules-core:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-modules-core/android/src/main/java/expo/modules/apploader/AppLoaderProvider.kt:34:52 Unchecked cast of 'java.lang.Class<*>!' to 'java.lang.Class<out expo.modules.apploader.HeadlessAppLoader>'.
> Task :react-native-webview:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:22:8 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:82:18 'var allowFileAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:83:18 'var allowUniversalAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:125:21 'fun allowScanningByMediaScanner(): Unit' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:162:36 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:301:14 'class MapBuilder : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:351:34 Condition is always 'true'.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:370:38 'var allowUniversalAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:431:51 Unchecked cast of 'kotlin.Any?' to 'java.util.HashMap<kotlin.String, kotlin.String>'.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:487:23 'var savePassword: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:488:23 'var saveFormData: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:558:23 'var allowFileAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:659:65 Unchecked cast of 'java.util.ArrayList<kotlin.Any?>' to 'kotlin.collections.List<kotlin.collections.Map<kotlin.String, kotlin.String>>'.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:680:23 'var saveFormData: Boolean' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:10:75 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:21:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:21:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:22:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:11:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:22:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:22:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:23:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:12:3 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:23:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:23:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:24:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:5:8 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:10:89 'constructor<T : (Event<Event<*>!>..Event<*>?)>(p0: Int): Event<T>' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:27:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:27:42 'interface RCTEventEmitter : JavaScriptModule' is deprecated. Use [RCTModernEventEmitter] instead.
w: file:///home/<USER>/workingdir/build/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:28:21 'fun receiveEvent(targetTag: Int, eventName: String, params: WritableMap?): Unit' is deprecated. Use [RCTModernEventEmitter.receiveEvent] instead.
> Task :react-native-webview:compileReleaseJavaWithJavac
> Task :app:processReleaseResources
> Task :react-native-webview:bundleLibCompileToJarRelease
> Task :react-native-reanimated:bundleLibRuntimeToJarRelease
> Task :react-native-gesture-handler:bundleLibRuntimeToJarRelease
> Task :react-native-reanimated:processReleaseJavaRes NO-SOURCE
> Task :react-native-reanimated:createFullJarRelease
> Task :react-native-gesture-handler:processReleaseJavaRes
> Task :react-native-reanimated:extractProguardFiles
> Task :react-native-gesture-handler:createFullJarRelease
> Task :react-native-gesture-handler:extractProguardFiles
> Task :react-native-gesture-handler:prepareLintJarForPublish
> Task :react-native-safe-area-context:bundleLibRuntimeToJarRelease
> Task :react-native-safe-area-context:processReleaseJavaRes
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.
> Task :react-native-safe-area-context:createFullJarRelease
> Task :react-native-safe-area-context:extractProguardFiles
> Task :expo-modules-core:compileReleaseJavaWithJavac
> Task :expo-modules-core:bundleLibCompileToJarRelease
> Task :expo-constants:compileReleaseKotlin
> Task :expo-constants:compileReleaseJavaWithJavac
> Task :expo-constants:bundleLibCompileToJarRelease
> Task :expo-dev-client:compileReleaseKotlin NO-SOURCE
> Task :expo-dev-client:compileReleaseJavaWithJavac
> Task :expo-dev-client:bundleLibCompileToJarRelease
> Task :expo-dev-menu-interface:compileReleaseKotlin
> Task :react-native-async-storage_async-storage:generateReleaseLintModel
> Task :expo-dev-menu-interface:compileReleaseJavaWithJavac
> Task :expo-dev-menu-interface:bundleLibCompileToJarRelease
> Task :expo-json-utils:compileReleaseKotlin
> Task :expo-updates-interface:compileReleaseKotlin
> Task :expo-updates-interface:compileReleaseJavaWithJavac
> Task :expo-json-utils:compileReleaseJavaWithJavac
> Task :expo-updates-interface:bundleLibCompileToJarRelease
> Task :expo-json-utils:bundleLibCompileToJarRelease
> Task :expo-image-loader:compileReleaseKotlin
> Task :expo-manifests:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-manifests/android/src/main/java/expo/modules/manifests/core/EmbeddedManifest.kt:19:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/expo-manifests/android/src/main/java/expo/modules/manifests/core/EmbeddedManifest.kt:19:86 'fun getLegacyID(): String' is deprecated. Prefer scopeKey or projectId depending on use case.
w: file:///home/<USER>/workingdir/build/node_modules/expo-manifests/android/src/main/java/expo/modules/manifests/core/ExpoUpdatesManifest.kt:16:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/expo-manifests/android/src/main/java/expo/modules/manifests/core/Manifest.kt:13:3 Deprecations and opt-ins on a method overridden from 'Any' may not be reported.
w: file:///home/<USER>/workingdir/build/node_modules/expo-manifests/android/src/main/java/expo/modules/manifests/core/Manifest.kt:15:12 'fun getRawJson(): JSONObject' is deprecated. Prefer to use specific field getters.
> Task :expo-image-loader:compileReleaseJavaWithJavac
> Task :expo-manifests:compileReleaseJavaWithJavac
> Task :expo-image-loader:bundleLibCompileToJarRelease
> Task :expo-manifests:bundleLibCompileToJarRelease
> Task :react-native-safe-area-context:generateReleaseLintModel
> Task :expo-constants:bundleLibRuntimeToJarRelease
> Task :expo-constants:processReleaseJavaRes
> Task :expo-constants:createFullJarRelease
> Task :expo-modules-core:bundleLibRuntimeToJarRelease
> Task :expo-modules-core:processReleaseJavaRes
> Task :expo-modules-core:createFullJarRelease
> Task :expo-modules-core:generateReleaseLintModel
> Task :expo-constants:generateReleaseLintModel
> Task :expo-dev-client:bundleLibRuntimeToJarRelease
> Task :expo-dev-client:processReleaseJavaRes NO-SOURCE
> Task :expo-dev-client:createFullJarRelease
> Task :expo-dev-client:generateReleaseLintModel
> Task :expo-dev-menu-interface:bundleLibRuntimeToJarRelease
> Task :expo-dev-menu-interface:processReleaseJavaRes
> Task :expo-dev-menu-interface:createFullJarRelease
> Task :expo-dev-menu-interface:generateReleaseLintModel
> Task :expo-json-utils:bundleLibRuntimeToJarRelease
> Task :expo-json-utils:processReleaseJavaRes
> Task :expo-json-utils:createFullJarRelease
> Task :expo-json-utils:generateReleaseLintModel
> Task :expo-manifests:bundleLibRuntimeToJarRelease
> Task :expo-manifests:processReleaseJavaRes
> Task :expo-manifests:createFullJarRelease
> Task :expo-manifests:generateReleaseLintModel
> Task :expo-updates-interface:bundleLibRuntimeToJarRelease
> Task :expo-updates-interface:processReleaseJavaRes
> Task :expo-updates-interface:createFullJarRelease
> Task :expo-updates-interface:generateReleaseLintModel
> Task :expo-image-loader:bundleLibRuntimeToJarRelease
> Task :expo-image-loader:processReleaseJavaRes
> Task :expo-image-loader:createFullJarRelease
> Task :react-native-reanimated:generateReleaseLintModel
> Task :react-native-async-storage_async-storage:prepareLintJarForPublish
> Task :react-native-reanimated:prepareLintJarForPublish
> Task :expo-image-loader:generateReleaseLintModel
> Task :react-native-safe-area-context:prepareLintJarForPublish
> Task :react-native-gesture-handler:generateReleaseLintModel
> Task :expo-image-manipulator:compileReleaseKotlin
> Task :react-native-screens:processReleaseJavaRes
> Task :react-native-screens:bundleLibRuntimeToJarRelease
> Task :react-native-screens:extractProguardFiles
> Task :react-native-screens:createFullJarRelease
> Task :react-native-svg:bundleLibRuntimeToJarRelease
> Task :react-native-svg:processReleaseJavaRes NO-SOURCE
> Task :react-native-svg:createFullJarRelease
> Task :react-native-svg:extractProguardFiles
> Task :react-native-svg:generateReleaseLintModel
> Task :react-native-svg:prepareLintJarForPublish
> Task :react-native-webview:bundleLibRuntimeToJarRelease
> Task :react-native-webview:processReleaseJavaRes
> Task :react-native-webview:createFullJarRelease
> Task :react-native-webview:extractProguardFiles
> Task :expo-image-manipulator:compileReleaseJavaWithJavac
> Task :expo-image-manipulator:bundleLibCompileToJarRelease
> Task :expo-image-manipulator:bundleLibRuntimeToJarRelease
> Task :expo-image-manipulator:processReleaseJavaRes
> Task :react-native-webview:generateReleaseLintModel
> Task :expo-image-manipulator:createFullJarRelease
> Task :react-native-webview:prepareLintJarForPublish
> Task :react-native-worklets:bundleLibRuntimeToJarRelease
> Task :expo-image-manipulator:generateReleaseLintModel
> Task :react-native-worklets:processReleaseJavaRes NO-SOURCE
> Task :react-native-worklets:createFullJarRelease
> Task :react-native-worklets:extractProguardFiles
> Task :react-native-worklets:generateReleaseLintModel
> Task :react-native-worklets:prepareLintJarForPublish
> Task :react-native-screens:generateReleaseLintModel
> Task :react-native-screens:prepareLintJarForPublish
> Task :react-native-gesture-handler:mergeReleaseJniLibFolders
> Task :react-native-gesture-handler:extractDeepLinksForAarRelease
> Task :react-native-safe-area-context:mergeReleaseJniLibFolders
> Task :react-native-safe-area-context:mergeReleaseNativeLibs NO-SOURCE
> Task :react-native-safe-area-context:stripReleaseDebugSymbols NO-SOURCE
> Task :react-native-safe-area-context:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-safe-area-context:extractDeepLinksForAarRelease
> Task :expo-dev-menu:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/com/facebook/react/devsupport/DevMenuSettingsBase.kt:6:8 'class PreferenceManager : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/com/facebook/react/devsupport/DevMenuSettingsBase.kt:18:51 'class PreferenceManager : Any' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/com/facebook/react/devsupport/DevMenuSettingsBase.kt:18:69 'static fun getDefaultSharedPreferences(p0: Context!): SharedPreferences!' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/com/facebook/react/devsupport/DevMenuSettingsBase.kt:51:13 This code uses error suppression for 'NOTHING_TO_OVERRIDE'. While it might compile and work, the compiler behavior is UNSPECIFIED and WON'T BE PRESERVED. Please report your use case to the Kotlin issue tracker instead: https://kotl.in/issue
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/com/facebook/react/devsupport/DevMenuSettingsBase.kt:58:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/expo/modules/devmenu/fab/MovableFloatingActionButton.kt:173:17 'fun computeBounds(p0: RectF, p1: Boolean): Unit' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-menu/android/src/main/java/expo/modules/devmenu/helpers/DevMenuOkHttpExtension.kt:58:19 'fun create(contentType: MediaType?, content: String): RequestBody' is deprecated. Moved to extension function. Put the 'content' argument first to fix Java.
> Task :react-native-reanimated:configureCMakeRelWithDebInfo[arm64-v8a]
Checking the license for package CMake 3.22.1 in /home/<USER>/Android/Sdk/licenses
License for package CMake 3.22.1 accepted.
Preparing "Install CMake 3.22.1 v.3.22.1".
"Install CMake 3.22.1 v.3.22.1" ready.
Installing CMake 3.22.1 in /home/<USER>/Android/Sdk/cmake/3.22.1
"Install CMake 3.22.1 v.3.22.1" complete.
"Install CMake 3.22.1 v.3.22.1" finished.
> Task :expo-dev-menu:compileReleaseJavaWithJavac
> Task :expo-dev-menu:bundleLibCompileToJarRelease
> Task :expo-dev-menu:bundleLibRuntimeToJarRelease
> Task :expo-dev-menu:processReleaseJavaRes
> Task :expo-dev-menu:createFullJarRelease
> Task :expo-dev-menu:generateReleaseLintModel
> Task :react-native-reanimated:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :expo-dev-launcher:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/DevLauncherRecentlyOpenedAppsRegistry.kt:32:47 Unchecked cast of 'kotlin.collections.MutableMap<kotlin.Any?, kotlin.Any?>' to 'kotlin.collections.MutableMap<kotlin.String, kotlin.Any>'.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/DevLauncherRecentlyOpenedAppsRegistry.kt:50:27 'fun getRawJson(): JSONObject' is deprecated. Prefer to use specific field getters.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:37:23 'constructor(p0: String!, p1: Bitmap!, p2: Int): ActivityManager.TaskDescription' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:63:61 'static field FLAG_TRANSLUCENT_STATUS: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:92:45 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:95:68 'static field SYSTEM_UI_FLAG_LIGHT_STATUS_BAR: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:99:67 'static field SYSTEM_UI_FLAG_LIGHT_STATUS_BAR: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:103:67 'static field SYSTEM_UI_FLAG_LIGHT_STATUS_BAR: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:107:15 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:115:59 'static field FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:116:61 'static field FLAG_FORCE_NOT_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:118:59 'static field FLAG_FORCE_NOT_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:119:61 'static field FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:131:23 'fun replaceSystemWindowInsets(p0: Int, p1: Int, p2: Int, p3: Int): WindowInsets' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:132:25 'val systemWindowInsetLeft: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:134:25 'val systemWindowInsetRight: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:135:25 'val systemWindowInsetBottom: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:150:15 'var statusBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:160:63 'static field FLAG_TRANSLUCENT_NAVIGATION: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:161:25 'var navigationBarColor: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:171:63 'static field FLAG_TRANSLUCENT_NAVIGATION: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:175:33 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:176:33 'static field SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:177:21 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:190:29 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:191:62 'static field SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:191:101 'static field SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:192:63 'static field SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:192:102 'static field SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:192:136 'static field SYSTEM_UI_FLAG_IMMERSIVE: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:193:70 'static field SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:193:109 'static field SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:193:143 'static field SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java.
w: file:///home/<USER>/workingdir/build/node_modules/expo-dev-launcher/android/src/main/java/expo/modules/devlauncher/launcher/configurators/DevLauncherExpoActivityConfigurator.kt:196:17 'var systemUiVisibility: Int' is deprecated. Deprecated in Java.
> Task :expo-dev-launcher:compileReleaseJavaWithJavac
> Task :expo-dev-launcher:bundleLibCompileToJarRelease
> Task :react-native-reanimated:configureCMakeRelWithDebInfo[x86]
> Task :expo-dev-launcher:bundleLibRuntimeToJarRelease
> Task :react-native-gesture-handler:extractReleaseAnnotations
> Task :react-native-safe-area-context:extractReleaseAnnotations
> Task :expo-dev-launcher:processReleaseJavaRes
> Task :react-native-gesture-handler:mergeReleaseGeneratedProguardFiles
> Task :react-native-gesture-handler:mergeReleaseConsumerProguardFiles
> Task :expo-dev-launcher:createFullJarRelease
> Task :react-native-gesture-handler:mergeReleaseShaders
> Task :react-native-gesture-handler:compileReleaseShaders NO-SOURCE
> Task :react-native-gesture-handler:generateReleaseAssets UP-TO-DATE
> Task :react-native-gesture-handler:packageReleaseAssets
> Task :react-native-gesture-handler:prepareReleaseArtProfile
> Task :expo-dev-launcher:generateReleaseLintModel
> Task :react-native-safe-area-context:mergeReleaseGeneratedProguardFiles
> Task :react-native-safe-area-context:mergeReleaseConsumerProguardFiles
> Task :react-native-safe-area-context:mergeReleaseShaders
> Task :react-native-safe-area-context:compileReleaseShaders
NO-SOURCE
> Task :react-native-safe-area-context:generateReleaseAssets
UP-TO-DATE
> Task :react-native-safe-area-context:packageReleaseAssets
> Task :react-native-safe-area-context:prepareReleaseArtProfile
> Task :react-native-gesture-handler:mergeReleaseJavaResource
> Task :react-native-safe-area-context:mergeReleaseJavaResource
> Task :react-native-safe-area-context:syncReleaseLibJars
> Task :react-native-safe-area-context:bundleReleaseLocalLintAar
> Task :react-native-gesture-handler:syncReleaseLibJars
> Task :react-native-webview:mergeReleaseJniLibFolders
> Task :react-native-webview:mergeReleaseNativeLibs NO-SOURCE
> Task :react-native-webview:stripReleaseDebugSymbols NO-SOURCE
> Task :react-native-webview:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-webview:extractDeepLinksForAarRelease
> Task :react-native-webview:extractReleaseAnnotations
> Task :react-native-webview:mergeReleaseGeneratedProguardFiles
> Task :react-native-webview:mergeReleaseConsumerProguardFiles
> Task :react-native-webview:mergeReleaseShaders
> Task :react-native-webview:compileReleaseShaders NO-SOURCE
> Task :react-native-webview:generateReleaseAssets UP-TO-DATE
> Task :react-native-webview:packageReleaseAssets
> Task :react-native-webview:prepareReleaseArtProfile
> Task :react-native-webview:mergeReleaseJavaResource
> Task :react-native-webview:syncReleaseLibJars
> Task :react-native-webview:bundleReleaseLocalLintAar
> Task :react-native-async-storage_async-storage:mergeReleaseJniLibFolders
> Task :react-native-async-storage_async-storage:mergeReleaseNativeLibs NO-SOURCE
> Task :react-native-async-storage_async-storage:stripReleaseDebugSymbols NO-SOURCE
> Task :react-native-async-storage_async-storage:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-async-storage_async-storage:extractDeepLinksForAarRelease
> Task :react-native-async-storage_async-storage:extractReleaseAnnotations
> Task :react-native-async-storage_async-storage:mergeReleaseGeneratedProguardFiles
> Task :react-native-async-storage_async-storage:mergeReleaseConsumerProguardFiles
> Task :react-native-async-storage_async-storage:mergeReleaseShaders
> Task :react-native-async-storage_async-storage:compileReleaseShaders NO-SOURCE
> Task :react-native-async-storage_async-storage:generateReleaseAssets UP-TO-DATE
> Task :react-native-async-storage_async-storage:packageReleaseAssets
> Task :react-native-async-storage_async-storage:prepareReleaseArtProfile
> Task :react-native-async-storage_async-storage:mergeReleaseJavaResource
> Task :react-native-async-storage_async-storage:syncReleaseLibJars
> Task :react-native-async-storage_async-storage:bundleReleaseLocalLintAar
> Task :expo:mergeReleaseJniLibFolders
> Task :expo:mergeReleaseNativeLibs NO-SOURCE
> Task :expo:stripReleaseDebugSymbols NO-SOURCE
> Task :expo:copyReleaseJniLibsProjectAndLocalJars
> Task :expo:extractDeepLinksForAarRelease
> Task :expo:mergeReleaseShaders
> Task :expo:compileReleaseShaders NO-SOURCE
> Task :expo:generateReleaseAssets UP-TO-DATE
> Task :expo:packageReleaseAssets
> Task :expo:prepareReleaseArtProfile
> Task :react-native-svg:mergeReleaseJniLibFolders
> Task :react-native-svg:mergeReleaseNativeLibs NO-SOURCE
> Task :react-native-svg:stripReleaseDebugSymbols NO-SOURCE
> Task :react-native-svg:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-svg:extractDeepLinksForAarRelease
> Task :react-native-svg:extractReleaseAnnotations
> Task :react-native-svg:mergeReleaseGeneratedProguardFiles
> Task :react-native-svg:mergeReleaseConsumerProguardFiles
> Task :react-native-svg:mergeReleaseShaders
> Task :react-native-svg:compileReleaseShaders NO-SOURCE
> Task :react-native-svg:generateReleaseAssets UP-TO-DATE
> Task :react-native-svg:packageReleaseAssets
> Task :react-native-svg:prepareReleaseArtProfile
> Task :react-native-svg:mergeReleaseJavaResource
> Task :react-native-svg:syncReleaseLibJars
> Task :react-native-svg:bundleReleaseLocalLintAar
> Task :react-native-reanimated:configureCMakeRelWithDebInfo[x86_64]
> Task :react-native-reanimated:generateJsonModelRelease
> Task :react-native-reanimated:prefabReleaseConfigurePackage
> Task :react-native-screens:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-worklets:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :expo:compileReleaseKotlin
w: file:///home/<USER>/workingdir/build/node_modules/expo/android/src/main/java/expo/modules/fetch/ExpoFetchModule.kt:30:39 'constructor(reactContext: ReactContext): ForwardingCookieHandler' is deprecated. Use the default constructor.
w: file:///home/<USER>/workingdir/build/node_modules/expo/android/src/main/java/expo/modules/fetch/NativeResponse.kt:41:16 This declaration overrides a deprecated member but is not marked as deprecated itself. Please add the '@Deprecated' annotation or suppress the diagnostic.
w: file:///home/<USER>/workingdir/build/node_modules/expo/android/src/main/java/expo/modules/fetch/NativeResponse.kt:43:11 'fun deallocate(): Unit' is deprecated. Use sharedObjectDidRelease() instead.
> Task :react-native-gesture-handler:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-gesture-handler:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :expo:compileReleaseJavaWithJavac
> Task :expo:bundleLibCompileToJarRelease
> Task :react-native-gesture-handler:configureCMakeRelWithDebInfo[x86]
> Task :expo:bundleLibRuntimeToJarRelease
> Task :expo:processReleaseJavaRes
> Task :expo:createFullJarRelease
> Task :app:compileReleaseKotlin
> Task :app:compileReleaseJavaWithJavac
> Task :expo:generateReleaseLintModel
> Task :app:generateReleaseLintVitalReportModel
> Task :expo:extractReleaseAnnotations
> Task :expo:mergeReleaseGeneratedProguardFiles
> Task :expo:mergeReleaseConsumerProguardFiles
> Task :expo:mergeReleaseJavaResource
> Task :expo:syncReleaseLibJars
> Task :expo:bundleReleaseLocalLintAar
> Task :react-native-gesture-handler:configureCMakeRelWithDebInfo[x86_64]
> Task :expo-dev-launcher:mergeReleaseJniLibFolders
> Task :expo-dev-launcher:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-dev-launcher:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-dev-launcher:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-dev-launcher:extractDeepLinksForAarRelease
> Task :expo-dev-launcher:extractReleaseAnnotations
> Task :expo-dev-launcher:mergeReleaseGeneratedProguardFiles
> Task :expo-dev-launcher:mergeReleaseConsumerProguardFiles
> Task :expo-dev-launcher:mergeReleaseShaders
> Task :expo-dev-launcher:compileReleaseShaders NO-SOURCE
> Task :expo-dev-launcher:generateReleaseAssets UP-TO-DATE
> Task :expo-dev-launcher:packageReleaseAssets
> Task :expo-dev-launcher:prepareReleaseArtProfile
> Task :expo-dev-launcher:mergeReleaseJavaResource
> Task :expo-dev-launcher:syncReleaseLibJars
> Task :expo-dev-launcher:bundleReleaseLocalLintAar
> Task :expo-dev-menu:mergeReleaseJniLibFolders
> Task :expo-dev-menu:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-dev-menu:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-dev-menu:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-dev-menu:extractDeepLinksForAarRelease
> Task :expo-dev-menu:extractReleaseAnnotations
> Task :expo-dev-menu:mergeReleaseGeneratedProguardFiles
> Task :expo-dev-menu:mergeReleaseConsumerProguardFiles
> Task :expo-dev-menu:mergeReleaseShaders
> Task :expo-dev-menu:compileReleaseShaders NO-SOURCE
> Task :expo-dev-menu:generateReleaseAssets UP-TO-DATE
> Task :expo-dev-menu:packageReleaseAssets
> Task :expo-dev-menu:prepareReleaseArtProfile
> Task :expo-dev-menu:mergeReleaseJavaResource
> Task :expo-dev-menu:syncReleaseLibJars
> Task :expo-dev-menu:bundleReleaseLocalLintAar
> Task :expo-dev-menu-interface:mergeReleaseJniLibFolders
> Task :expo-dev-menu-interface:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-dev-menu-interface:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-dev-menu-interface:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-dev-menu-interface:extractDeepLinksForAarRelease
> Task :expo-dev-menu-interface:extractReleaseAnnotations
> Task :expo-dev-menu-interface:mergeReleaseGeneratedProguardFiles
> Task :expo-dev-menu-interface:mergeReleaseConsumerProguardFiles
> Task :expo-dev-menu-interface:mergeReleaseShaders
> Task :expo-dev-menu-interface:compileReleaseShaders NO-SOURCE
> Task :expo-dev-menu-interface:generateReleaseAssets UP-TO-DATE
> Task :expo-dev-menu-interface:packageReleaseAssets
> Task :expo-dev-menu-interface:prepareReleaseArtProfile
> Task :expo-dev-menu-interface:mergeReleaseJavaResource
> Task :expo-dev-menu-interface:syncReleaseLibJars
> Task :expo-dev-menu-interface:bundleReleaseLocalLintAar
> Task :expo-modules-core:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-screens:buildCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-screens:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :react-native-reanimated:buildCMakeRelWithDebInfo[arm64-v8a][reanimated,worklets]
> Task :react-native-worklets:buildCMakeRelWithDebInfo[arm64-v8a][worklets]
> Task :react-native-screens:buildCMakeRelWithDebInfo[armeabi-v7a]
> Task :expo-modules-core:buildCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-screens:configureCMakeRelWithDebInfo[x86]
> Task :react-native-screens:buildCMakeRelWithDebInfo[x86]
> Task :react-native-screens:configureCMakeRelWithDebInfo[x86_64]
> Task :react-native-worklets:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :react-native-screens:buildCMakeRelWithDebInfo[x86_64]
> Task :react-native-screens:mergeReleaseJniLibFolders
> Task :react-native-screens:mergeReleaseNativeLibs
> Task :react-native-screens:stripReleaseDebugSymbols
> Task :react-native-screens:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-screens:extractDeepLinksForAarRelease
> Task :react-native-screens:extractReleaseAnnotations
> Task :react-native-screens:mergeReleaseGeneratedProguardFiles
> Task :react-native-screens:mergeReleaseConsumerProguardFiles
> Task :react-native-screens:mergeReleaseShaders
> Task :react-native-screens:compileReleaseShaders NO-SOURCE
> Task :react-native-screens:generateReleaseAssets UP-TO-DATE
> Task :react-native-screens:packageReleaseAssets
> Task :react-native-screens:prepareReleaseArtProfile
> Task :react-native-screens:mergeReleaseJavaResource
> Task :react-native-screens:syncReleaseLibJars
> Task :react-native-screens:bundleReleaseLocalLintAar
> Task :expo-constants:mergeReleaseJniLibFolders
> Task :expo-constants:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-constants:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-constants:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-constants:extractDeepLinksForAarRelease
> Task :expo-constants:extractReleaseAnnotations
> Task :expo-constants:mergeReleaseGeneratedProguardFiles
> Task :expo-constants:mergeReleaseConsumerProguardFiles
> Task :expo-constants:mergeReleaseShaders
> Task :expo-constants:compileReleaseShaders NO-SOURCE
> Task :expo-constants:generateReleaseAssets UP-TO-DATE
> Task :expo-constants:packageReleaseAssets
> Task :expo-constants:prepareReleaseArtProfile
> Task :expo-constants:mergeReleaseJavaResource
> Task :expo-constants:syncReleaseLibJars
> Task :expo-constants:bundleReleaseLocalLintAar
> Task :expo-dev-client:mergeReleaseJniLibFolders
> Task :expo-dev-client:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-dev-client:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-dev-client:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-dev-client:extractDeepLinksForAarRelease
> Task :expo-dev-client:extractReleaseAnnotations
> Task :expo-dev-client:mergeReleaseGeneratedProguardFiles
> Task :expo-dev-client:mergeReleaseConsumerProguardFiles
> Task :expo-dev-client:mergeReleaseShaders
> Task :expo-dev-client:compileReleaseShaders NO-SOURCE
> Task :expo-dev-client:generateReleaseAssets UP-TO-DATE
> Task :expo-dev-client:packageReleaseAssets
> Task :expo-dev-client:prepareReleaseArtProfile
> Task :expo-dev-client:mergeReleaseJavaResource
> Task :expo-dev-client:syncReleaseLibJars
> Task :expo-dev-client:bundleReleaseLocalLintAar
> Task :expo-image-loader:mergeReleaseJniLibFolders
> Task :expo-image-loader:mergeReleaseNativeLibs
NO-SOURCE
> Task :expo-image-loader:stripReleaseDebugSymbols
NO-SOURCE
> Task :expo-image-loader:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-image-loader:extractDeepLinksForAarRelease
> Task :expo-image-loader:extractReleaseAnnotations
> Task :expo-image-loader:mergeReleaseGeneratedProguardFiles
> Task :expo-image-loader:mergeReleaseConsumerProguardFiles
> Task :expo-image-loader:mergeReleaseShaders
> Task :expo-image-loader:compileReleaseShaders NO-SOURCE
> Task :expo-image-loader:generateReleaseAssets UP-TO-DATE
> Task :expo-image-loader:packageReleaseAssets
> Task :expo-image-loader:prepareReleaseArtProfile
> Task :expo-image-loader:mergeReleaseJavaResource
> Task :expo-image-loader:syncReleaseLibJars
> Task :expo-image-loader:bundleReleaseLocalLintAar
> Task :expo-image-manipulator:mergeReleaseJniLibFolders
> Task :expo-image-manipulator:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-image-manipulator:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-image-manipulator:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-image-manipulator:extractDeepLinksForAarRelease
> Task :expo-image-manipulator:extractReleaseAnnotations
> Task :expo-image-manipulator:mergeReleaseGeneratedProguardFiles
> Task :expo-image-manipulator:mergeReleaseConsumerProguardFiles
> Task :expo-image-manipulator:mergeReleaseShaders
> Task :expo-image-manipulator:compileReleaseShaders NO-SOURCE
> Task :expo-image-manipulator:generateReleaseAssets UP-TO-DATE
> Task :expo-image-manipulator:packageReleaseAssets
> Task :expo-image-manipulator:prepareReleaseArtProfile
> Task :expo-image-manipulator:mergeReleaseJavaResource
> Task :expo-image-manipulator:syncReleaseLibJars
> Task :expo-image-manipulator:bundleReleaseLocalLintAar
> Task :expo-manifests:mergeReleaseJniLibFolders
> Task :expo-manifests:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-manifests:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-manifests:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-manifests:extractDeepLinksForAarRelease
> Task :expo-manifests:extractReleaseAnnotations
> Task :expo-manifests:mergeReleaseGeneratedProguardFiles
> Task :expo-manifests:mergeReleaseConsumerProguardFiles
> Task :expo-manifests:mergeReleaseShaders
> Task :expo-manifests:compileReleaseShaders NO-SOURCE
> Task :expo-manifests:generateReleaseAssets UP-TO-DATE
> Task :expo-manifests:packageReleaseAssets
> Task :expo-manifests:prepareReleaseArtProfile
> Task :expo-manifests:mergeReleaseJavaResource
> Task :expo-manifests:syncReleaseLibJars
> Task :expo-manifests:bundleReleaseLocalLintAar
> Task :expo-json-utils:mergeReleaseJniLibFolders
> Task :expo-json-utils:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-json-utils:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-json-utils:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-json-utils:extractDeepLinksForAarRelease
> Task :expo-json-utils:extractReleaseAnnotations
> Task :expo-json-utils:mergeReleaseGeneratedProguardFiles
> Task :expo-json-utils:mergeReleaseConsumerProguardFiles
> Task :expo-json-utils:mergeReleaseShaders
> Task :expo-json-utils:compileReleaseShaders NO-SOURCE
> Task :expo-json-utils:generateReleaseAssets UP-TO-DATE
> Task :expo-json-utils:packageReleaseAssets
> Task :expo-json-utils:prepareReleaseArtProfile
> Task :expo-json-utils:mergeReleaseJavaResource
> Task :expo-json-utils:syncReleaseLibJars
> Task :expo-json-utils:bundleReleaseLocalLintAar
> Task :expo-updates-interface:mergeReleaseJniLibFolders
> Task :expo-updates-interface:mergeReleaseNativeLibs NO-SOURCE
> Task :expo-updates-interface:stripReleaseDebugSymbols NO-SOURCE
> Task :expo-updates-interface:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-updates-interface:extractDeepLinksForAarRelease
> Task :expo-updates-interface:extractReleaseAnnotations
> Task :expo-updates-interface:mergeReleaseGeneratedProguardFiles
> Task :expo-updates-interface:mergeReleaseConsumerProguardFiles
> Task :expo-updates-interface:mergeReleaseShaders
> Task :expo-updates-interface:compileReleaseShaders NO-SOURCE
> Task :expo-updates-interface:generateReleaseAssets UP-TO-DATE
> Task :expo-updates-interface:packageReleaseAssets
> Task :expo-updates-interface:prepareReleaseArtProfile
> Task :expo-updates-interface:mergeReleaseJavaResource
> Task :expo-updates-interface:syncReleaseLibJars
> Task :expo-updates-interface:bundleReleaseLocalLintAar
> Task :expo-constants:writeReleaseLintModelMetadata
> Task :expo-dev-client:writeReleaseLintModelMetadata
> Task :expo-dev-launcher:writeReleaseLintModelMetadata
> Task :expo-dev-menu:writeReleaseLintModelMetadata
> Task :expo-dev-menu-interface:writeReleaseLintModelMetadata
> Task :expo-image-loader:writeReleaseLintModelMetadata
> Task :expo-image-manipulator:writeReleaseLintModelMetadata
> Task :expo-json-utils:writeReleaseLintModelMetadata
> Task :expo-manifests:writeReleaseLintModelMetadata
> Task :expo-updates-interface:writeReleaseLintModelMetadata
> Task :expo:writeReleaseLintModelMetadata
> Task :react-native-worklets:buildCMakeRelWithDebInfo[armeabi-v7a][worklets]
> Task :react-native-async-storage_async-storage:lintVitalAnalyzeRelease
> Task :expo-modules-core:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :react-native-async-storage_async-storage:writeReleaseLintModelMetadata
> Task :react-native-gesture-handler:writeReleaseLintModelMetadata
> Task :react-native-worklets:configureCMakeRelWithDebInfo[x86]
> Task :react-native-reanimated:buildCMakeRelWithDebInfo[armeabi-v7a][reanimated,worklets]
> Task :expo-modules-core:buildCMakeRelWithDebInfo[armeabi-v7a]
> Task :react-native-safe-area-context:lintVitalAnalyzeRelease
> Task :react-native-worklets:buildCMakeRelWithDebInfo[x86][worklets]
> Task :react-native-safe-area-context:writeReleaseLintModelMetadata
> Task :react-native-worklets:configureCMakeRelWithDebInfo[x86_64]
> Task :react-native-screens:lintVitalAnalyzeRelease
> Task :expo-modules-core:configureCMakeRelWithDebInfo[x86]
> Task :react-native-worklets:buildCMakeRelWithDebInfo[x86_64][worklets]
> Task :react-native-screens:writeReleaseLintModelMetadata
> Task :react-native-reanimated:buildCMakeRelWithDebInfo[x86][reanimated,worklets]
> Task :expo-modules-core:buildCMakeRelWithDebInfo[x86]
> Task :react-native-svg:lintVitalAnalyzeRelease
> Task :react-native-svg:writeReleaseLintModelMetadata
> Task :react-native-worklets:mergeReleaseJniLibFolders
> Task :react-native-worklets:mergeReleaseNativeLibs
> Task :react-native-worklets:stripReleaseDebugSymbols
> Task :react-native-worklets:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-worklets:extractDeepLinksForAarRelease
> Task :react-native-worklets:extractReleaseAnnotations
> Task :react-native-worklets:mergeReleaseGeneratedProguardFiles
> Task :react-native-worklets:mergeReleaseConsumerProguardFiles
> Task :react-native-worklets:mergeReleaseShaders
> Task :react-native-worklets:compileReleaseShaders NO-SOURCE
> Task :react-native-worklets:generateReleaseAssets UP-TO-DATE
> Task :react-native-worklets:packageReleaseAssets
> Task :react-native-worklets:externalNativeBuildRelease
> Task :react-native-worklets:generateJsonModelRelease
> Task :react-native-worklets:prefabReleaseConfigurePackage
> Task :react-native-worklets:prefabReleasePackage
> Task :react-native-worklets:prepareReleaseArtProfile
> Task :react-native-worklets:mergeReleaseJavaResource
> Task :react-native-worklets:syncReleaseLibJars
> Task :react-native-worklets:bundleReleaseLocalLintAar
> Task :react-native-webview:lintVitalAnalyzeRelease
> Task :react-native-webview:writeReleaseLintModelMetadata
> Task :expo:generateReleaseLintVitalModel
> Task :react-native-worklets:writeReleaseLintModelMetadata
> Task :expo-constants:generateReleaseLintVitalModel
> Task :expo-dev-client:generateReleaseLintVitalModel
> Task :expo-dev-launcher:generateReleaseLintVitalModel
> Task :expo-dev-menu:generateReleaseLintVitalModel
> Task :expo-dev-menu-interface:generateReleaseLintVitalModel
> Task :expo-image-loader:generateReleaseLintVitalModel
> Task :expo-image-manipulator:generateReleaseLintVitalModel
> Task :expo-json-utils:generateReleaseLintVitalModel
> Task :expo-manifests:generateReleaseLintVitalModel
> Task :expo-updates-interface:generateReleaseLintVitalModel
> Task :react-native-async-storage_async-storage:generateReleaseLintVitalModel
> Task :react-native-gesture-handler:generateReleaseLintVitalModel
> Task :react-native-safe-area-context:generateReleaseLintVitalModel
> Task :react-native-screens:generateReleaseLintVitalModel
> Task :react-native-svg:generateReleaseLintVitalModel
> Task :react-native-webview:generateReleaseLintVitalModel
> Task :react-native-worklets:generateReleaseLintVitalModel
> Task :app:configureCMakeRelWithDebInfo[arm64-v8a]
> Task :app:configureCMakeRelWithDebInfo[armeabi-v7a]
> Task :app:configureCMakeRelWithDebInfo[x86]
> Task :react-native-worklets:lintVitalAnalyzeRelease
> Task :expo:copyReleaseJniLibsProjectOnly
> Task :expo-constants:copyReleaseJniLibsProjectOnly
> Task :expo-dev-client:copyReleaseJniLibsProjectOnly
> Task :expo-dev-launcher:copyReleaseJniLibsProjectOnly
> Task :expo-dev-menu:copyReleaseJniLibsProjectOnly
> Task :expo-dev-menu-interface:copyReleaseJniLibsProjectOnly
> Task :expo-image-loader:copyReleaseJniLibsProjectOnly
> Task :expo-image-manipulator:copyReleaseJniLibsProjectOnly
> Task :expo-json-utils:copyReleaseJniLibsProjectOnly
> Task :expo-manifests:copyReleaseJniLibsProjectOnly
> Task :expo-updates-interface:copyReleaseJniLibsProjectOnly
> Task :react-native-async-storage_async-storage:copyReleaseJniLibsProjectOnly
> Task :react-native-safe-area-context:copyReleaseJniLibsProjectOnly
> Task :react-native-screens:copyReleaseJniLibsProjectOnly
> Task :react-native-svg:copyReleaseJniLibsProjectOnly
> Task :react-native-webview:copyReleaseJniLibsProjectOnly
> Task :react-native-worklets:copyReleaseJniLibsProjectOnly
> Task :react-native-gesture-handler:bundleLibRuntimeToDirRelease
> Task :app:configureCMakeRelWithDebInfo[x86_64]
> Task :app:mergeReleaseJniLibFolders
> Task :react-native-safe-area-context:bundleLibRuntimeToDirRelease
> Task :react-native-screens:bundleLibRuntimeToDirRelease
> Task :react-native-webview:bundleLibRuntimeToDirRelease
> Task :react-native-async-storage_async-storage:bundleLibRuntimeToDirRelease
> Task :expo:bundleLibRuntimeToDirRelease
> Task :react-native-svg:bundleLibRuntimeToDirRelease
> Task :react-native-worklets:bundleLibRuntimeToDirRelease
> Task :expo-dev-launcher:bundleLibRuntimeToDirRelease
> Task :expo-dev-menu:bundleLibRuntimeToDirRelease
> Task :expo-dev-menu-interface:bundleLibRuntimeToDirRelease
> Task :expo-constants:bundleLibRuntimeToDirRelease
> Task :expo-dev-client:bundleLibRuntimeToDirRelease
> Task :expo-image-loader:bundleLibRuntimeToDirRelease
> Task :expo-image-manipulator:bundleLibRuntimeToDirRelease
> Task :expo-manifests:bundleLibRuntimeToDirRelease
> Task :expo-json-utils:bundleLibRuntimeToDirRelease
> Task :expo-updates-interface:bundleLibRuntimeToDirRelease
> Task :expo-modules-core:configureCMakeRelWithDebInfo[x86_64]
> Task :app:checkReleaseDuplicateClasses
> Task :app:dexBuilderRelease
> Task :app:desugarReleaseFileDependencies
> Task :app:mergeReleaseStartupProfile
> Task :react-native-reanimated:buildCMakeRelWithDebInfo[x86_64][reanimated,worklets]
> Task :expo-modules-core:buildCMakeRelWithDebInfo[x86_64]
> Task :app:mergeExtDexRelease
> Task :expo-modules-core:mergeReleaseJniLibFolders
> Task :expo-modules-core:extractDeepLinksForAarRelease
> Task :expo-modules-core:extractReleaseAnnotations
> Task :expo-modules-core:mergeReleaseGeneratedProguardFiles
> Task :expo-modules-core:mergeReleaseConsumerProguardFiles
> Task :expo-modules-core:mergeReleaseNativeLibs
> Task :react-native-reanimated:externalNativeBuildRelease
> Task :react-native-reanimated:prefabReleasePackage
> Task :react-native-gesture-handler:buildCMakeRelWithDebInfo[arm64-v8a]
> Task :react-native-gesture-handler:buildCMakeRelWithDebInfo[armeabi-v7a]
> Task :react-native-gesture-handler:buildCMakeRelWithDebInfo[x86]
> Task :react-native-gesture-handler:buildCMakeRelWithDebInfo[x86_64]
> Task :expo-modules-core:mergeReleaseShaders
> Task :react-native-reanimated:mergeReleaseJniLibFolders
> Task :react-native-gesture-handler:mergeReleaseNativeLibs
> Task :expo-modules-core:stripReleaseDebugSymbols
> Task :react-native-reanimated:extractDeepLinksForAarRelease
> Task :react-native-reanimated:extractReleaseAnnotations
> Task :react-native-reanimated:mergeReleaseGeneratedProguardFiles
> Task :react-native-reanimated:mergeReleaseConsumerProguardFiles
> Task :react-native-reanimated:mergeReleaseShaders
> Task :react-native-reanimated:compileReleaseShaders NO-SOURCE
> Task :react-native-reanimated:generateReleaseAssets UP-TO-DATE
> Task :react-native-reanimated:packageReleaseAssets
> Task :react-native-reanimated:prepareReleaseArtProfile
> Task :react-native-reanimated:mergeReleaseJavaResource
> Task :react-native-reanimated:syncReleaseLibJars
> Task :react-native-reanimated:mergeReleaseNativeLibs
> Task :react-native-gesture-handler:stripReleaseDebugSymbols
> Task :expo-modules-core:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-modules-core:compileReleaseShaders NO-SOURCE
> Task :expo-modules-core:generateReleaseAssets UP-TO-DATE
> Task :react-native-gesture-handler:copyReleaseJniLibsProjectAndLocalJars
> Task :react-native-gesture-handler:bundleReleaseLocalLintAar
> Task :react-native-reanimated:stripReleaseDebugSymbols
> Task :expo-modules-core:packageReleaseAssets
> Task :expo-modules-core:prepareReleaseArtProfile
> Task :react-native-reanimated:writeReleaseLintModelMetadata
> Task :expo-modules-core:mergeReleaseJavaResource
> Task :react-native-reanimated:copyReleaseJniLibsProjectAndLocalJars
> Task :expo-modules-core:syncReleaseLibJars
> Task :expo-modules-core:bundleReleaseLocalLintAar
> Task :react-native-reanimated:bundleReleaseLocalLintAar
> Task :expo-modules-core:writeReleaseLintModelMetadata
> Task :react-native-reanimated:lintVitalAnalyzeRelease
> Task :expo:lintVitalAnalyzeRelease
> Task :expo-constants:lintVitalAnalyzeRelease
> Task :react-native-gesture-handler:lintVitalAnalyzeRelease
> Task :expo-dev-client:lintVitalAnalyzeRelease
> Task :expo-image-loader:lintVitalAnalyzeRelease
> Task :expo-image-manipulator:lintVitalAnalyzeRelease
> Task :expo-json-utils:lintVitalAnalyzeRelease
> Task :expo-dev-menu-interface:lintVitalAnalyzeRelease
> Task :expo-dev-launcher:lintVitalAnalyzeRelease
> Task :expo-updates-interface:lintVitalAnalyzeRelease
> Task :expo-modules-core:generateReleaseLintVitalModel
> Task :react-native-reanimated:generateReleaseLintVitalModel
> Task :expo-dev-menu:lintVitalAnalyzeRelease
> Task :expo-modules-core:copyReleaseJniLibsProjectOnly
> Task :react-native-gesture-handler:copyReleaseJniLibsProjectOnly
> Task :expo-manifests:lintVitalAnalyzeRelease
> Task :react-native-reanimated:bundleLibRuntimeToDirRelease
> Task :react-native-reanimated:copyReleaseJniLibsProjectOnly
> Task :expo-modules-core:bundleLibRuntimeToDirRelease
> Task :app:buildCMakeRelWithDebInfo[arm64-v8a]
C/C++: ninja: Entering directory `/home/<USER>/workingdir/build/android/app/.cxx/RelWithDebInfo/194f316l/arm64-v8a'
C/C++: /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dappmodules_EXPORTS -I/home/<USER>/workingdir/build/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/home/<USER>/workingdir/build/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/home/<USER>/workingdir/build/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem /home/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/0022c038c951d6e7d28f4306e8d52cd5/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/0022c038c951d6e7d28f4306e8d52cd5/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -MD -MT CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -MF CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o.d -o CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp
C/C++: In file included from /home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp:22:
C/C++: /home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/./rnworklets.h:22:18: error: redefinition of 'NativeWorkletsModuleSpecJSI'
C/C++:    22 | class JSI_EXPORT NativeWorkletsModuleSpecJSI : public JavaTurboModule {
C/C++:       |                  ^
C/C++: /home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/./rnreanimated.h:30:18: note: previous definition is here
C/C++:    30 | class JSI_EXPORT NativeWorkletsModuleSpecJSI : public JavaTurboModule {
C/C++:       |                  ^
C/C++: 1 error generated.
> Task :app:buildCMakeRelWithDebInfo[arm64-v8a] FAILED
> Task :expo-modules-core:lintVitalAnalyzeRelease
[Incubating] Problems report is available at: file:///home/<USER>/workingdir/build/android/build/reports/problems/problems-report.html
FAILURE: Build failed with an exception.
* What went wrong:
Execution failed for task ':app:buildCMakeRelWithDebInfo[arm64-v8a]'.
> com.android.ide.common.process.ProcessException: ninja: Entering directory `/home/<USER>/workingdir/build/android/app/.cxx/RelWithDebInfo/194f316l/arm64-v8a'
  [0/2] Re-checking globbed directories...
  [1/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
  [2/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
  [3/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
  [4/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
  [5/76] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
  [6/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
  [7/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
  [8/76] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
  [9/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
  [10/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
  [11/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
  [12/76] Building CXX object CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
  FAILED: CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o 
  /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dappmodules_EXPORTS -I/home/<USER>/workingdir/build/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni -I/home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/home/<USER>/workingdir/build/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/workingdir/build/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/home/<USER>/workingdir/build/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -I/home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem /home/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/0022c038c951d6e7d28f4306e8d52cd5/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/0022c038c951d6e7d28f4306e8d52cd5/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -MD -MT CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -MF CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o.d -o CMakeFiles/appmodules.dir/home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp
  In file included from /home/<USER>/workingdir/build/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp:22:
  /home/<USER>/workingdir/build/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/./rnworklets.h:22:18: error: redefinition of 'NativeWorkletsModuleSpecJSI'
     22 | class JSI_EXPORT NativeWorkletsModuleSpecJSI : public JavaTurboModule {
        |                  ^
  /home/<USER>/workingdir/build/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/./rnreanimated.h:30:18: note: previous definition is here
     30 | class JSI_EXPORT NativeWorkletsModuleSpecJSI : public JavaTurboModule {
        |                  ^
  1 error generated.
  [13/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
  [14/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
  [15/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
  [16/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
  [17/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
  ninja: build stopped: subcommand failed.
  
  C++ build system [build] failed while executing:
      /home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja \
        -C \
        /home/<USER>/workingdir/build/android/app/.cxx/RelWithDebInfo/194f316l/arm64-v8a \
        appmodules \
        react_codegen_rnscreens \
        react_codegen_rnsvg \
        react_codegen_safeareacontext
    from /home/<USER>/workingdir/build/android/app
* Try:
> Run with
--stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.
BUILD FAILED in 11m 47s
Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.
You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.
For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.
880 actionable tasks: 879 executed, 1 up-to-date
Error: Gradle build failed with unknown error. See logs for the "Run gradlew" phase for more information.